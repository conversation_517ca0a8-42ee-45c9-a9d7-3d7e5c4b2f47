﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Communication.ECU;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Configuration;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Services;
using VolvoFlashWR.Core.Utilities;
using VolvoFlashWR.UI.ViewModels;
using VolvoFlashWR.UI.Views;

namespace VolvoFlashWR.UI
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        // Initialize fields with null! to indicate they will be initialized before use
        private ILoggingService _loggingService = null!;
        private IAppConfigurationService _configurationService = null!;
        private ILicensingService _licensingService = null!;
        private IVocomService _vocomService = null!;
        private IECUCommunicationService _ecuCommunicationService = null!;
        private IBackupService _backupService = null!;
        private IBackupSchedulerService _backupSchedulerService = null!;
        public IFlashOperationMonitorService _flashOperationMonitorService = null!;

        // Flag to track if we're already handling an unhandled exception
        private bool _isHandlingUnhandledException = false;

        public App()
        {
            // Subscribe to unhandled exception events
            AppDomain.CurrentDomain.UnhandledException += OnAppDomainUnhandledException;
            DispatcherUnhandledException += OnDispatcherUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Create and show main window immediately with a loading state
            var mainWindow = new MainWindow();
            mainWindow.Show();

            // Initialize services in the background
            Task.Run(async () =>
            {
                try
                {
                    // Initialize services
                    await InitializeServicesAsync();

                    // Check license status
                    if (!_licensingService.IsLicensed() && !_licensingService.IsInTrialPeriod())
                    {
                        // Show license activation dialog on UI thread
                        await Dispatcher.InvokeAsync(() =>
                        {
                            var licenseViewModel = new LicenseViewModel(_loggingService, _licensingService);
                            var licenseView = new LicenseView(licenseViewModel)
                            {
                                WindowStartupLocation = WindowStartupLocation.CenterScreen
                            };

                            bool? result = licenseView.ShowDialog();

                            // If still not licensed or in trial, exit the application
                            if (!_licensingService.IsLicensed() && !_licensingService.IsInTrialPeriod())
                            {
                                _loggingService.LogWarning("Application not licensed and not in trial period, shutting down", "App");
                                MessageBox.Show("The application is not licensed. Please activate the software to continue.",
                                    "License Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                                Shutdown();
                                return;
                            }
                        });
                    }

                    // Create main view model on UI thread
                    await Dispatcher.InvokeAsync(() =>
                    {
                        var mainViewModel = new MainViewModel(
                            _loggingService,
                            _configurationService,
                            _vocomService,
                            _ecuCommunicationService,
                            _backupService,
                            _backupSchedulerService,
                            _flashOperationMonitorService);

                        // Set the DataContext
                        mainWindow.DataContext = mainViewModel;
                    });
                }
                catch (Exception ex)
                {
                    // Handle errors on UI thread
                    await Dispatcher.InvokeAsync(() =>
                    {
                        // Log the error
                        if (_loggingService != null)
                        {
                            _loggingService.LogError($"Error initializing application: {ex.Message}", "App");
                            _loggingService.LogError($"Stack trace: {ex.StackTrace}", "App");

                            if (ex.InnerException != null)
                            {
                                _loggingService.LogError($"Inner exception: {ex.InnerException.Message}", "App");
                                _loggingService.LogError($"Inner exception stack trace: {ex.InnerException.StackTrace}", "App");
                            }
                        }
                        else
                        {
                            // Logging service not initialized, write to console
                            Console.Error.WriteLine($"Error initializing application: {ex.Message}");
                            Console.Error.WriteLine($"Stack trace: {ex.StackTrace}");

                            if (ex.InnerException != null)
                            {
                                Console.Error.WriteLine($"Inner exception: {ex.InnerException.Message}");
                                Console.Error.WriteLine($"Inner exception stack trace: {ex.InnerException.StackTrace}");
                            }
                        }

                        // Show error message to user
                        MessageBox.Show($"Error initializing application: {ex.Message}\n\nPlease check the logs for more details.",
                            "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);

                        // Don't shutdown immediately - let the user see the error
                    });
                }
            });
        }

        private async Task InitializeServicesAsync()
        {
            // Create logging service
            string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            _loggingService = new LoggingService();

            // Check if verbose logging is enabled
            bool verboseLogging = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("VERBOSE_LOGGING"));
            await _loggingService.InitializeAsync(logPath, verboseLogging);

            if (verboseLogging)
            {
                _loggingService.LogInformation("Verbose logging enabled", "App");
            }

            // Initialize configuration service
            _configurationService = new AppConfigurationService(_loggingService);
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "app_config.json");
            bool configInitialized = await _configurationService.InitializeAsync(configPath);

            if (configInitialized)
            {
                _loggingService.LogInformation("Configuration service initialized successfully", "App");
            }
            else
            {
                _loggingService.LogWarning("Failed to initialize configuration service, using default values", "App");
            }

            // Check if we should use dummy implementations - first from config, then from environment variable
            bool useDummyImplementations = _configurationService.GetValue<bool>("Application.UseDummyImplementations", false);

            // If not set in config, check environment variable
            if (!useDummyImplementations)
            {
                string dummyEnv = Environment.GetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS");
                useDummyImplementations = !string.IsNullOrEmpty(dummyEnv) && dummyEnv.ToLower() != "false";

                // If found in environment, update the config for next time
                if (useDummyImplementations)
                {
                    await _configurationService.SetValueAsync("Application.UseDummyImplementations", true);
                }
                else
                {
                    // If explicitly set to false in environment, update config
                    if (dummyEnv?.ToLower() == "false")
                    {
                        await _configurationService.SetValueAsync("Application.UseDummyImplementations", false);
                    }
                }
            }

            if (useDummyImplementations)
            {
                _loggingService.LogInformation("Using dummy implementations for all services", "App");

                try
                {
                    // Create dummy Vocom service
                    _loggingService.LogInformation("Creating dummy Vocom service", "App");
                    _vocomService = new DummyVocomService(_loggingService);
                    bool vocomInitialized = await _vocomService.InitializeAsync();

                    if (vocomInitialized)
                    {
                        _loggingService.LogInformation("Dummy Vocom service initialized successfully", "App");
                    }
                    else
                    {
                        _loggingService.LogWarning("Dummy Vocom service initialization returned false", "App");
                    }

                    // Create dummy ECU communication service
                    _loggingService.LogInformation("Creating dummy ECU communication service", "App");
                    _ecuCommunicationService = new DummyECUCommunicationService(_loggingService);
                    bool ecuInitialized = await _ecuCommunicationService.InitializeAsync(_vocomService);

                    if (ecuInitialized)
                    {
                        _loggingService.LogInformation("Dummy ECU communication service initialized successfully", "App");
                    }
                    else
                    {
                        _loggingService.LogWarning("Dummy ECU communication service initialization returned false", "App");
                    }
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"Error initializing dummy services: {ex.Message}", "App");
                    _loggingService.LogError($"Stack trace: {ex.StackTrace}", "App");
                    throw;
                }
            }
            else
            {
                // Check if we should use patched implementation
                string patchedEnv = Environment.GetEnvironmentVariable("USE_PATCHED_IMPLEMENTATION");
                bool usePatchedImplementation = !string.IsNullOrEmpty(patchedEnv) && patchedEnv.ToLower() != "false";

                _loggingService.LogInformation($"USE_PATCHED_IMPLEMENTATION environment variable is set to: '{patchedEnv}'", "App");
                _loggingService.LogInformation($"usePatchedImplementation flag is: {usePatchedImplementation}", "App");

                // Check if verbose logging is enabled
                string verboseLoggingEnv = Environment.GetEnvironmentVariable("VERBOSE_LOGGING");
                bool verboseLoggingEnabled = !string.IsNullOrEmpty(verboseLoggingEnv) && verboseLoggingEnv.ToLower() != "false";
                _loggingService.LogInformation($"VERBOSE_LOGGING environment variable is set to: '{verboseLoggingEnv}'", "App");
                _loggingService.LogInformation($"verboseLogging flag is: {verboseLoggingEnabled}", "App");

                // Create Vocom service with retry logic
                object vocomFactory;

                try
                {
                    if (usePatchedImplementation)
                    {
                        _loggingService.LogInformation("*** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***", "App");

                        // Check if the PatchedVocomServiceFactory type exists
                        Type patchedFactoryType = Type.GetType("VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory, VolvoFlashWR.Communication");
                        if (patchedFactoryType != null)
                        {
                            _loggingService.LogInformation($"Found PatchedVocomServiceFactory type: {patchedFactoryType.FullName}", "App");

                            // Create an instance using reflection
                            object instance = Activator.CreateInstance(patchedFactoryType, new object[] { _loggingService });
                            vocomFactory = instance;

                            _loggingService.LogInformation("Successfully created PatchedVocomServiceFactory instance using reflection", "App");
                        }
                        else
                        {
                            _loggingService.LogError("PatchedVocomServiceFactory type not found in assembly", "App");
                            _loggingService.LogInformation("Falling back to standard VocomServiceFactory", "App");
                            vocomFactory = new VocomServiceFactory(_loggingService);
                        }
                    }
                    else
                    {
                        _loggingService.LogInformation("Creating standard VocomServiceFactory instance", "App");
                        vocomFactory = new VocomServiceFactory(_loggingService);
                        _loggingService.LogInformation("Successfully created standard VocomServiceFactory instance", "App");
                    }
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"Error creating VocomServiceFactory: {ex.Message}", "App", ex);
                    _loggingService.LogInformation("Falling back to standard VocomServiceFactory", "App");
                    vocomFactory = new VocomServiceFactory(_loggingService);
                }

                _loggingService.LogInformation($"Using {vocomFactory.GetType().FullName} Vocom service factory", "App");

                int maxRetries = 3;
                int retryCount = 0;
                bool success = false;

                // First, ensure PTT is disconnected before attempting to create the Vocom service
                _loggingService.LogInformation("Checking if PTT application is running before creating Vocom service", "App");
                bool pttDisconnected = await ConnectionHelper.DisconnectPTTApplicationAsync();
                if (!pttDisconnected)
                {
                    _loggingService.LogWarning("Failed to disconnect PTT application, attempting to force terminate", "App");
                    bool forceTerminated = ConnectionHelper.ForceTerminatePTTProcess();
                    if (forceTerminated)
                    {
                        _loggingService.LogInformation("Successfully force terminated PTT application", "App");
                        // Give it a moment to fully release resources
                        await Task.Delay(1000);
                    }
                    else
                    {
                        _loggingService.LogWarning("Failed to force terminate PTT application, Vocom connection may fail", "App");
                    }
                }

                while (!success && retryCount < maxRetries)
                {
                    try
                    {
                        _loggingService.LogInformation($"Creating Vocom service (attempt {retryCount + 1}/{maxRetries})", "App");

                        // Use reflection to call CreateServiceAsync method
                        var createServiceMethod = vocomFactory.GetType().GetMethod("CreateServiceAsync");
                        if (createServiceMethod != null)
                        {
                            var task = (Task<IVocomService>)createServiceMethod.Invoke(vocomFactory, null);
                            _vocomService = await task;
                        }
                        else
                        {
                            _loggingService.LogError("CreateServiceAsync method not found on factory", "App");
                            _vocomService = null;
                        }

                        if (_vocomService == null)
                        {
                            retryCount++;
                            _loggingService.LogWarning($"Failed to create Vocom service on attempt {retryCount}/{maxRetries}", "App");
                            await Task.Delay(1000 * retryCount); // Increasing delay between retries
                            continue;
                        }

                        // Ensure Vocom service is properly initialized
                        _loggingService.LogInformation("Initializing Vocom service", "App");
                        bool vocomInitialized = await _vocomService.InitializeAsync();

                        if (!vocomInitialized)
                        {
                            retryCount++;
                            _loggingService.LogWarning($"Failed to initialize Vocom service on attempt {retryCount}/{maxRetries}", "App");
                            await Task.Delay(1000 * retryCount); // Increasing delay between retries
                            continue;
                        }

                        success = true;
                    }
                    catch (Exception ex)
                    {
                        retryCount++;
                        _loggingService.LogWarning($"Error creating/initializing Vocom service on attempt {retryCount}/{maxRetries}: {ex.Message}", "App");
                        await Task.Delay(1000 * retryCount); // Increasing delay between retries
                    }
                }

                if (!success)
                {
                    _loggingService.LogError($"Failed to create/initialize Vocom service after {maxRetries} attempts", "App");

                    // Create a dummy Vocom service to allow the application to continue
                    _loggingService.LogWarning("Creating a dummy Vocom service to allow the application to continue", "App");
                    _vocomService = new DummyVocomService(_loggingService);
                    await _vocomService.InitializeAsync();
                }

                // Try to connect to a Vocom device if available
                try
                {
                    var vocomDevices = await _vocomService.ScanForDevicesAsync();
                    if (vocomDevices.Count > 0)
                    {
                        var firstDevice = vocomDevices[0];
                        if (firstDevice != null)
                        {
                            _loggingService.LogInformation($"Found {vocomDevices.Count} Vocom devices, attempting to connect to the first one", "App");
                            bool connected = await _vocomService.ConnectAsync(firstDevice);
                            if (!connected)
                            {
                                _loggingService.LogWarning("Failed to connect to Vocom device, continuing without a connected device", "App");
                            }
                            else
                            {
                                _loggingService.LogInformation($"Connected to Vocom device {firstDevice.Name}", "App");
                            }
                        }
                    }
                    else
                    {
                        _loggingService.LogWarning("No Vocom devices found, continuing without a connected device", "App");
                    }
                }
                catch (Exception ex)
                {
                    _loggingService.LogWarning($"Error scanning for or connecting to Vocom devices: {ex.Message}", "App");
                }

                // Create ECU communication service with retry logic
                // Ensure Vocom service is not null before passing it to the factory
                var vocomServiceToUse = NullableHelper.ThrowIfNull(_vocomService, nameof(_vocomService));
                var ecuFactory = new ECUCommunicationServiceFactory(_loggingService, vocomServiceToUse);
                _ecuCommunicationService = await ecuFactory.CreateServiceAsync();

                if (_ecuCommunicationService == null)
                {
                    _loggingService.LogError("Failed to create ECU communication service, application may have limited functionality", "App");
                    MessageBox.Show("Failed to create ECU communication service. The application will continue with limited functionality.",
                        "Initialization Warning", MessageBoxButton.OK, MessageBoxImage.Warning);

                    // Create a dummy ECU communication service to allow the application to continue
                    _ecuCommunicationService = new DummyECUCommunicationService(_loggingService);
                    await _ecuCommunicationService.InitializeAsync(_vocomService);
                }
            }

            // Create backup service with predefined categories and tags
            var backupFactory = new BackupServiceFactory(_loggingService, _ecuCommunicationService);

            // Define standard categories and tags - get from config if available
            var predefinedCategories = _configurationService.GetValue<List<string>>("Backup.PredefinedCategories", new List<string>
            {
                "Production",
                "Development",
                "Testing",
                "Archived",
                "Critical"
            });

            var predefinedTags = _configurationService.GetValue<List<string>>("Backup.PredefinedTags", new List<string>
            {
                "Important",
                "Verified",
                "Debug",
                "Stable",
                "Beta",
                "Experimental",
                "Approved"
            });

            // Create custom backup directory path - get from config if available
            string backupPath = _configurationService.GetValue<string>("Backup.BackupPath",
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups"));

            // Create the service with predefined categories and tags
            bool useCompression = _configurationService.GetValue<bool>("Backup.UseCompression", true);
            bool useEncryption = _configurationService.GetValue<bool>("Backup.UseEncryption", false);

            var backupServiceResult = await backupFactory.CreateServiceWithCategoriesAndTagsAsync(
                predefinedCategories,
                predefinedTags,
                backupPath,
                useCompression: useCompression,
                useEncryption: useEncryption);

            // Ensure the backup service is not null
            _backupService = NullableHelper.ThrowIfNull(backupServiceResult, nameof(backupServiceResult));

            // The null check is now handled by the NullableHelper.ThrowIfNull method above

            // Create backup scheduler service
            var schedulerFactory = new BackupSchedulerServiceFactory(_loggingService, _backupService, _ecuCommunicationService);
            var schedulerServiceResult = await schedulerFactory.CreateServiceAsync();

            // Ensure the scheduler service is not null
            _backupSchedulerService = NullableHelper.ThrowIfNull(schedulerServiceResult, nameof(schedulerServiceResult));

            // Create flash operation monitor service
            _flashOperationMonitorService = new FlashOperationMonitorService(_loggingService);
            bool monitorInitialized = await _flashOperationMonitorService.InitializeAsync();

            if (monitorInitialized)
            {
                _loggingService.LogInformation("Flash operation monitor service initialized successfully", "App");
            }
            else
            {
                _loggingService.LogWarning("Failed to initialize flash operation monitor service", "App");
            }

            // Initialize licensing service
            _licensingService = new LicensingService(_loggingService, _configurationService);
            bool licenseInitialized = await _licensingService.InitializeAsync();

            if (licenseInitialized)
            {
                _loggingService.LogInformation("Licensing service initialized successfully", "App");

                // Log license status
                var licenseInfo = _licensingService.GetLicenseInfo();
                _loggingService.LogInformation($"License status: {licenseInfo.Status}", "App");

                if (licenseInfo.Status == LicenseStatus.Trial)
                {
                    _loggingService.LogInformation($"Trial period: {licenseInfo.TrialDaysRemaining} days remaining", "App");
                }
                else if (licenseInfo.Status == LicenseStatus.Licensed)
                {
                    _loggingService.LogInformation($"License expires on: {licenseInfo.ExpirationDate?.ToString("yyyy-MM-dd")}", "App");
                }
            }
            else
            {
                _loggingService.LogWarning("Failed to initialize licensing service", "App");
            }

            // Create some default backup schedules if none exist
            var schedules = await _backupSchedulerService.GetAllSchedulesAsync();
            if (schedules.Count == 0)
            {
                _loggingService.LogInformation("Creating default backup schedules", "App");

                // Scan for ECUs to create schedules for
                var ecuDevices = await _ecuCommunicationService.ScanForECUsAsync();
                if (ecuDevices.Count > 0)
                {
                    // Create a daily backup schedule for the first ECU
                    var ecu = ecuDevices[0];
                    if (ecu == null)
                    {
                        _loggingService.LogWarning("First ECU device is null, cannot create default schedules", "App");
                        return;
                    }
                    var dailySchedule = new BackupSchedule
                    {
                        Name = "Daily Backup - " + ecu.Name,
                        Description = "Automatic daily backup at 3:00 AM",
                        ECUId = ecu.Id,
                        ECUName = ecu.Name,
                        FrequencyType = BackupFrequencyType.Daily,
                        Interval = 1,
                        TimeOfDay = new TimeSpan(3, 0, 0), // 3:00 AM
                        Category = "Automated",
                        Tags = new List<string> { "Daily", "Automated" },
                        MaxBackupsToKeep = 7 // Keep a week's worth of backups
                    };

                    await _backupSchedulerService.CreateScheduleAsync(dailySchedule);
                    _loggingService.LogInformation($"Created daily backup schedule for {ecu.Name}", "App");

                    // Create a weekly backup schedule for the first ECU
                    var weeklySchedule = new BackupSchedule
                    {
                        Name = "Weekly Backup - " + ecu.Name,
                        Description = "Automatic weekly backup on Sunday at 4:00 AM",
                        ECUId = ecu.Id,
                        ECUName = ecu.Name,
                        FrequencyType = BackupFrequencyType.Weekly,
                        DaysOfWeek = new List<DayOfWeek> { DayOfWeek.Sunday },
                        TimeOfDay = new TimeSpan(4, 0, 0), // 4:00 AM
                        Category = "Automated",
                        Tags = new List<string> { "Weekly", "Automated" },
                        MaxBackupsToKeep = 4 // Keep a month's worth of backups
                    };

                    await _backupSchedulerService.CreateScheduleAsync(weeklySchedule);
                    _loggingService.LogInformation($"Created weekly backup schedule for {ecu.Name}", "App");
                }
            }
        }

        /// <summary>
        /// Gets a service of the specified type
        /// </summary>
        /// <typeparam name="T">The type of service to get</typeparam>
        /// <returns>The service instance, or null if not found</returns>
        public T GetService<T>() where T : class
        {
            if (typeof(T) == typeof(ILoggingService))
            {
                return _loggingService as T;
            }
            else if (typeof(T) == typeof(IAppConfigurationService))
            {
                return _configurationService as T;
            }
            else if (typeof(T) == typeof(IVocomService))
            {
                return _vocomService as T;
            }
            else if (typeof(T) == typeof(IECUCommunicationService))
            {
                return _ecuCommunicationService as T;
            }
            else if (typeof(T) == typeof(IBackupService))
            {
                return _backupService as T;
            }
            else if (typeof(T) == typeof(IBackupSchedulerService))
            {
                return _backupSchedulerService as T;
            }
            else if (typeof(T) == typeof(IFlashOperationMonitorService))
            {
                return _flashOperationMonitorService as T;
            }
            else if (typeof(T) == typeof(ILicensingService))
            {
                return _licensingService as T;
            }

            return null;
        }

        #region Global Exception Handlers

        /// <summary>
        /// Handles unhandled exceptions in the AppDomain
        /// </summary>
        private void OnAppDomainUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (_isHandlingUnhandledException)
            {
                return;
            }

            _isHandlingUnhandledException = true;

            try
            {
                var exception = e.ExceptionObject as Exception;
                bool isTerminating = e.IsTerminating;

                LogUnhandledException(exception, "AppDomain.UnhandledException", isTerminating);

                // Show error message to user
                ShowUnhandledExceptionMessage(exception, "Application Error", isTerminating);
            }
            finally
            {
                _isHandlingUnhandledException = false;

                // If the exception is terminal, shut down the application
                if (e.IsTerminating)
                {
                    Shutdown();
                }
            }
        }

        /// <summary>
        /// Handles unhandled exceptions in the UI thread
        /// </summary>
        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            if (_isHandlingUnhandledException)
            {
                return;
            }

            _isHandlingUnhandledException = true;

            try
            {
                LogUnhandledException(e.Exception, "DispatcherUnhandledException", false);

                // Show error message to user
                ShowUnhandledExceptionMessage(e.Exception, "UI Thread Error", false);

                // Mark the exception as handled to prevent application crash
                e.Handled = true;
            }
            finally
            {
                _isHandlingUnhandledException = false;
            }
        }

        /// <summary>
        /// Handles unobserved task exceptions
        /// </summary>
        private void OnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            if (_isHandlingUnhandledException)
            {
                return;
            }

            _isHandlingUnhandledException = true;

            try
            {
                LogUnhandledException(e.Exception, "TaskScheduler.UnobservedTaskException", false);

                // Show error message to user
                ShowUnhandledExceptionMessage(e.Exception, "Background Task Error", false);

                // Mark the exception as observed to prevent application crash
                e.SetObserved();
            }
            finally
            {
                _isHandlingUnhandledException = false;
            }
        }

        /// <summary>
        /// Logs an unhandled exception
        /// </summary>
        private void LogUnhandledException(Exception exception, string source, bool isTerminating)
        {
            if (exception == null)
            {
                return;
            }

            try
            {
                // Log to the logging service if available
                if (_loggingService != null)
                {
                    if (isTerminating)
                    {
                        _loggingService.LogError($"Unhandled exception (Terminating): {exception.Message}", source, exception);
                    }
                    else
                    {
                        _loggingService.LogError($"Unhandled exception: {exception.Message}", source, exception);
                    }

                    // Log detailed exception information
                    _loggingService.LogDebug($"Detailed exception information: {exception.GetDetailedMessage(true)}", source);
                }
                else
                {
                    // Logging service not initialized, write to console
                    Console.Error.WriteLine($"Unhandled exception ({source}): {exception.Message}");
                    Console.Error.WriteLine($"Stack trace: {exception.StackTrace}");

                    if (exception.InnerException != null)
                    {
                        Console.Error.WriteLine($"Inner exception: {exception.InnerException.Message}");
                        Console.Error.WriteLine($"Inner exception stack trace: {exception.InnerException.StackTrace}");
                    }
                }
            }
            catch (Exception ex)
            {
                // Last resort logging
                Console.Error.WriteLine($"Error logging unhandled exception: {ex.Message}");
                Console.Error.WriteLine($"Original exception: {exception.Message}");
            }
        }

        /// <summary>
        /// Shows an error message for an unhandled exception
        /// </summary>
        private void ShowUnhandledExceptionMessage(Exception exception, string title, bool isTerminating)
        {
            if (exception == null)
            {
                return;
            }

            try
            {
                // Get a user-friendly error message
                string userMessage = exception.GetUserFriendlyMessage();

                // Add additional information for terminating exceptions
                string message = isTerminating
                    ? $"{userMessage}\n\nThe application will now close. Please check the logs for more details."
                    : $"{userMessage}\n\nThe application will attempt to continue, but may be in an unstable state. Please save your work and restart the application.";

                // Show the message box on the UI thread
                Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
            catch (Exception ex)
            {
                // Last resort message
                Console.Error.WriteLine($"Error showing error message: {ex.Message}");
                Console.Error.WriteLine($"Original exception: {exception.Message}");
            }
        }

        #endregion
    }
}
