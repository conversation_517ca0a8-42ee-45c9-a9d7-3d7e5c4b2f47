using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.Configuration
{
    /// <summary>
    /// Service for managing application configuration
    /// </summary>
    public class AppConfigurationService : IAppConfigurationService
    {
        private readonly ILoggingService _logger;
        private readonly Dictionary<string, object> _configValues;
        private string _configFilePath;
        private bool _isInitialized;
        private bool _isModified;

        /// <summary>
        /// Event triggered when configuration is changed
        /// </summary>
        public event EventHandler<string> ConfigurationChanged;

        /// <summary>
        /// Gets the path to the configuration file
        /// </summary>
        public string ConfigFilePath => _configFilePath;

        /// <summary>
        /// Gets whether the configuration has been modified since last save
        /// </summary>
        public bool IsModified => _isModified;

        /// <summary>
        /// Initializes a new instance of the AppConfigurationService class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public AppConfigurationService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configValues = new Dictionary<string, object>();
            _isInitialized = false;
            _isModified = false;
        }

        /// <summary>
        /// Initializes the configuration service
        /// </summary>
        /// <param name="configFilePath">Path to the configuration file (optional)</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync(string configFilePath = null)
        {
            try
            {
                _logger.LogInformation("Initializing configuration service", "AppConfigurationService");

                // Set default config file path if not provided
                if (string.IsNullOrEmpty(configFilePath))
                {
                    _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "app_config.json");
                }
                else
                {
                    _configFilePath = configFilePath;
                }

                // Ensure the directory exists
                string configDirectory = Path.GetDirectoryName(_configFilePath);
                if (!string.IsNullOrEmpty(configDirectory))
                {
                    Directory.CreateDirectory(configDirectory);
                    _logger.LogInformation($"Created configuration directory: {configDirectory}", "AppConfigurationService");
                }

                // Load configuration if file exists, otherwise create default
                if (File.Exists(_configFilePath))
                {
                    bool loaded = await LoadConfigurationAsync();
                    if (!loaded)
                    {
                        _logger.LogWarning("Failed to load configuration, creating default", "AppConfigurationService");
                        await CreateDefaultConfigurationAsync();
                    }
                }
                else
                {
                    _logger.LogInformation("Configuration file not found, creating default", "AppConfigurationService");
                    await CreateDefaultConfigurationAsync();
                }

                _isInitialized = true;
                _logger.LogInformation("Configuration service initialized successfully", "AppConfigurationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing configuration service", "AppConfigurationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets a configuration value by key
        /// </summary>
        /// <typeparam name="T">The type of the configuration value</typeparam>
        /// <param name="key">The configuration key</param>
        /// <param name="defaultValue">The default value to return if the key is not found</param>
        /// <returns>The configuration value, or the default value if not found</returns>
        public T GetValue<T>(string key, T defaultValue = default)
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Configuration service not initialized", "AppConfigurationService");
                return defaultValue;
            }

            if (string.IsNullOrEmpty(key))
            {
                _logger.LogWarning("Configuration key is null or empty", "AppConfigurationService");
                return defaultValue;
            }

            if (_configValues.TryGetValue(key, out object value))
            {
                try
                {
                    if (value is JsonElement jsonElement)
                    {
                        // Handle JsonElement conversion
                        return ConvertJsonElementToType<T>(jsonElement, defaultValue);
                    }
                    else if (value is T typedValue)
                    {
                        return typedValue;
                    }
                    else
                    {
                        // Try to convert the value to the requested type
                        return (T)Convert.ChangeType(value, typeof(T));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error converting configuration value for key '{key}'", "AppConfigurationService", ex);
                    return defaultValue;
                }
            }

            _logger.LogDebug($"Configuration key '{key}' not found, returning default value", "AppConfigurationService");
            return defaultValue;
        }

        /// <summary>
        /// Sets a configuration value
        /// </summary>
        /// <typeparam name="T">The type of the configuration value</typeparam>
        /// <param name="key">The configuration key</param>
        /// <param name="value">The configuration value</param>
        /// <returns>True if the value was set successfully, false otherwise</returns>
        public async Task<bool> SetValueAsync<T>(string key, T value)
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Configuration service not initialized", "AppConfigurationService");
                return false;
            }

            if (string.IsNullOrEmpty(key))
            {
                _logger.LogWarning("Configuration key is null or empty", "AppConfigurationService");
                return false;
            }

            try
            {
                // Update or add the value
                if (_configValues.ContainsKey(key))
                {
                    _configValues[key] = value;
                }
                else
                {
                    _configValues.Add(key, value);
                }

                _isModified = true;
                _logger.LogDebug($"Configuration value set for key '{key}'", "AppConfigurationService");

                // Notify subscribers
                ConfigurationChanged?.Invoke(this, key);

                // Auto-save configuration
                await SaveConfigurationAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error setting configuration value for key '{key}'", "AppConfigurationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets all configuration values
        /// </summary>
        /// <returns>Dictionary of all configuration values</returns>
        public Dictionary<string, object> GetAllValues()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Configuration service not initialized", "AppConfigurationService");
                return new Dictionary<string, object>();
            }

            // Return a copy of the configuration values
            return new Dictionary<string, object>(_configValues);
        }

        /// <summary>
        /// Saves the current configuration to the file
        /// </summary>
        /// <returns>True if the configuration was saved successfully, false otherwise</returns>
        public async Task<bool> SaveConfigurationAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Configuration service not initialized", "AppConfigurationService");
                return false;
            }

            try
            {
                // Ensure the directory exists
                string configDirectory = Path.GetDirectoryName(_configFilePath);
                if (!string.IsNullOrEmpty(configDirectory))
                {
                    Directory.CreateDirectory(configDirectory);
                }

                // Serialize the configuration values
                string json = JsonSerializer.Serialize(_configValues, new JsonSerializerOptions { WriteIndented = true });

                // Write to file
                await File.WriteAllTextAsync(_configFilePath, json);

                _isModified = false;
                _logger.LogInformation($"Configuration saved to {_configFilePath}", "AppConfigurationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error saving configuration", "AppConfigurationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Loads the configuration from the file
        /// </summary>
        /// <returns>True if the configuration was loaded successfully, false otherwise</returns>
        public async Task<bool> LoadConfigurationAsync()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    _logger.LogWarning($"Configuration file not found: {_configFilePath}", "AppConfigurationService");
                    return false;
                }

                // Read the file
                string json = await File.ReadAllTextAsync(_configFilePath);

                // Deserialize the configuration
                var options = new JsonSerializerOptions { AllowTrailingCommas = true };
                var loadedConfig = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json, options);

                if (loadedConfig == null)
                {
                    _logger.LogWarning("Failed to deserialize configuration", "AppConfigurationService");
                    return false;
                }

                // Clear existing configuration
                _configValues.Clear();

                // Add loaded values
                foreach (var kvp in loadedConfig)
                {
                    _configValues.Add(kvp.Key, kvp.Value);
                }

                _isModified = false;
                _logger.LogInformation($"Configuration loaded from {_configFilePath}", "AppConfigurationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error loading configuration", "AppConfigurationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Resets the configuration to default values
        /// </summary>
        /// <returns>True if the configuration was reset successfully, false otherwise</returns>
        public async Task<bool> ResetToDefaultsAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Configuration service not initialized", "AppConfigurationService");
                return false;
            }

            try
            {
                // Clear existing configuration
                _configValues.Clear();

                // Create default configuration
                await CreateDefaultConfigurationAsync();

                _logger.LogInformation("Configuration reset to defaults", "AppConfigurationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error resetting configuration to defaults", "AppConfigurationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Creates default configuration values
        /// </summary>
        private async Task<bool> CreateDefaultConfigurationAsync()
        {
            try
            {
                // Clear existing configuration
                _configValues.Clear();

                // Add default values
                _configValues.Add("Application.Name", "VolvoFlashWR");
                _configValues.Add("Application.Version", "1.0.0");
                _configValues.Add("Logging.Enabled", true);
                _configValues.Add("Logging.DetailedLogging", false);
                _configValues.Add("Logging.LogPath", "Logs");
                _configValues.Add("Backup.Enabled", true);
                _configValues.Add("Backup.UseCompression", true);
                _configValues.Add("Backup.UseEncryption", false);
                _configValues.Add("Backup.BackupPath", "Backups");
                _configValues.Add("Backup.MaxBackupsToKeep", 10);
                _configValues.Add("Vocom.AutoConnect", true);
                _configValues.Add("Vocom.PreferredConnectionType", "USB");
                _configValues.Add("Vocom.ConnectionTimeoutMs", 5000);
                _configValues.Add("Vocom.RetryAttempts", 3);
                _configValues.Add("ECU.AutoScan", true);
                _configValues.Add("ECU.OperatingMode", "Bench");
                _configValues.Add("UI.Theme", "Light");
                _configValues.Add("UI.Language", "en-US");

                // Save the default configuration
                await SaveConfigurationAsync();

                _logger.LogInformation("Default configuration created", "AppConfigurationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating default configuration", "AppConfigurationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Converts a JsonElement to the specified type
        /// </summary>
        /// <typeparam name="T">The type to convert to</typeparam>
        /// <param name="element">The JsonElement to convert</param>
        /// <param name="defaultValue">The default value to return if conversion fails</param>
        /// <returns>The converted value, or the default value if conversion fails</returns>
        private T ConvertJsonElementToType<T>(JsonElement element, T defaultValue)
        {
            try
            {
                switch (element.ValueKind)
                {
                    case JsonValueKind.String:
                        if (typeof(T) == typeof(string))
                        {
                            return (T)(object)element.GetString();
                        }
                        else if (typeof(T) == typeof(Guid))
                        {
                            return (T)(object)Guid.Parse(element.GetString());
                        }
                        else if (typeof(T) == typeof(TimeSpan))
                        {
                            return (T)(object)TimeSpan.Parse(element.GetString());
                        }
                        else if (typeof(T) == typeof(DateTime))
                        {
                            return (T)(object)DateTime.Parse(element.GetString());
                        }
                        else
                        {
                            return (T)Convert.ChangeType(element.GetString(), typeof(T));
                        }

                    case JsonValueKind.Number:
                        if (typeof(T) == typeof(int))
                        {
                            return (T)(object)element.GetInt32();
                        }
                        else if (typeof(T) == typeof(long))
                        {
                            return (T)(object)element.GetInt64();
                        }
                        else if (typeof(T) == typeof(float))
                        {
                            return (T)(object)element.GetSingle();
                        }
                        else if (typeof(T) == typeof(double))
                        {
                            return (T)(object)element.GetDouble();
                        }
                        else if (typeof(T) == typeof(decimal))
                        {
                            return (T)(object)element.GetDecimal();
                        }
                        else
                        {
                            return (T)Convert.ChangeType(element.GetDouble(), typeof(T));
                        }

                    case JsonValueKind.True:
                    case JsonValueKind.False:
                        if (typeof(T) == typeof(bool))
                        {
                            return (T)(object)element.GetBoolean();
                        }
                        else
                        {
                            return (T)Convert.ChangeType(element.GetBoolean(), typeof(T));
                        }

                    case JsonValueKind.Null:
                        return defaultValue;

                    case JsonValueKind.Object:
                    case JsonValueKind.Array:
                        // For complex types, deserialize the element
                        return JsonSerializer.Deserialize<T>(element.GetRawText());

                    default:
                        return defaultValue;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error converting JsonElement to type {typeof(T).Name}", "AppConfigurationService", ex);
                return defaultValue;
            }
        }
    }
}
