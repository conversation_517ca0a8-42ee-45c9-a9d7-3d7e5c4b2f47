<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <StartupObject>VolvoFlashWR.Launcher.STAProgram</StartupObject>
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <ApplicationUseCompatibleTextRenderingDefault>true</ApplicationUseCompatibleTextRenderingDefault>
    <ApplicationDefaultFont>Segoe UI, 9pt</ApplicationDefaultFont>
    <EnableDefaultApplicationDefinition>false</EnableDefaultApplicationDefinition>
    <!-- <ApplicationIcon>VolvoFlashWR.ico</ApplicationIcon> -->
    <Version>1.0.0</Version>
    <Authors>S.A.H Software Solutions</Authors>
    <Company>S.A.H Software Solutions</Company>
    <Product>Volvo Flash WR</Product>
    <Description>ECU Management Tool for Volvo vehicles</Description>
    <Copyright>© 2025 All Rights Reserved For S.A.H Software Solutions Company</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PlatformTarget>x64</PlatformTarget>
    <PublishReadyToRun>false</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\VolvoFlashWR.UI\VolvoFlashWR.UI.csproj" />
  </ItemGroup>

  <!-- Copy all required libraries for real hardware mode -->
  <ItemGroup>
    <!-- Critical Vocom Hardware Libraries -->
    <None Include="..\Libraries\WUDFPuma.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\WUDFUpdate_01009.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\WdfCoInstaller01009.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\winusbcoinstaller2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\wudfpuma.inf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\WUDFPuma.cat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\wudfpuma.PNF">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Volvo/Phoenix Communication Libraries -->
    <None Include="..\Libraries\Volvo.ApciPlus.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.ApciPlus.dll.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.ApciPlusData.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.ApciPlusData.dll.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.ApciPlusTea2Data.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.ApciPlus.Simulator.dll.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\apci.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\apcidb.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\ApciPlusFuck.dll.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Volvo NAMS and NVS Libraries -->
    <None Include="..\Libraries\Volvo.NAMS.AC.Services.Interface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.NAMS.AC.Services.Interfaces.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.NVS.Core.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.NVS.Logging.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.NVS.Persistence.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Volvo.NVS.Persistence.NHibernate.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- VolvoIt Framework Libraries -->
    <None Include="..\Libraries\VolvoIt.Baf.Utility.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\VolvoIt.Fido.Agent.Gateway.Contract.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\VolvoIt.Waf.ServiceContract.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\VolvoIt.Waf.Utility.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Vodia Integration Libraries -->
    <None Include="..\Libraries\Vodia.CommonDomain.Model.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Vodia.Contracts.Common.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Vodia.UtilityComponent.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Support Libraries -->
    <None Include="..\Libraries\SystemInterface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\Ionic.Zip.Reduced.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\SharpCompress.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\WinSCPnet.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\WinSCP.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Data Libraries -->
    <None Include="..\Libraries\System.Data.SQLite.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Data.SqlServerCe.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Documentation -->
    <None Include="..\Libraries\whatisnew.rtf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Essential System Libraries for Runtime -->
    <None Include="..\Libraries\System.Memory.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Memory.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Numerics.Vectors.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Numerics.Vectors.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Runtime.CompilerServices.Unsafe.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Runtime.CompilerServices.Unsafe.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Threading.Tasks.Extensions.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Threading.Tasks.Extensions.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.ValueTuple.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- I/O and Compression Libraries -->
    <None Include="..\Libraries\System.IO.Compression.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.IO.Compression.ZipFile.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Network Libraries -->
    <None Include="..\Libraries\System.Net.Http.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Net.Sockets.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Net.Security.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- Text and XML Processing -->
    <None Include="..\Libraries\System.Text.RegularExpressions.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Xml.ReaderWriter.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\Libraries\System.Xml.XDocument.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <!-- <ItemGroup>
    <None Remove="VolvoFlashWR.ico" />
    <Content Include="VolvoFlashWR.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup> -->

</Project>
