# VolvoFlashWR Application

## Running the Application

There are several ways to run the VolvoFlashWR application:

### Option 1: Using the run scripts (Recommended)

- Windows: Double-click on `run_volvo_flash_wr.bat`
- PowerShell: Run `.\run.ps1`

### Option 2: Using dotnet CLI

```bash
# From the root directory
cd VolvoFlashWR.Launcher
dotnet run --framework net8.0-windows
```

### Option 3: Specifying the project from the root directory

```bash
# From the root directory
dotnet run --project VolvoFlashWR.Launcher --framework net8.0-windows
```

> **Important Note**: This application requires the Single-Threaded Apartment (STA) model for the UI thread because it uses WPF components. The `--framework net8.0-windows` parameter is required to ensure the application runs correctly.

## Vocom Driver Integration

The application integrates with the Vocom hardware adapter using the official Vocom driver. To use the application with real hardware:

1. Install the Vocom1 driver (CommunicationUnitInstaller-*******.msi)
2. Connect your Vocom adapter via USB, Bluetooth, or WiFi
3. Launch the application in "Normal Mode" (requires hardware)

The application will automatically locate the Vocom driver files in the following locations:
- `C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll`
- Windows driver store: `C:\Windows\System32\DriverStore\FileRepository\wudfpuma.inf_amd64_*\WUDFPuma.dll`

### Custom Driver Path Configuration

If the application cannot find the Vocom driver automatically, you can specify a custom path in the configuration file:

1. Open `VolvoFlashWR.UI\Config\vocom_config.json`
2. Set the `Vocom.DriverDllPath` value to the full path of your WUDFPuma.dll file
3. Save the file and restart the application

Example configuration:
```json
{
  "Vocom": {
    "DriverDllPath": "C:\\Path\\To\\Your\\WUDFPuma.dll",
    "AutoConnect": true
  }
}
```

## Troubleshooting

### STA Thread Error

If you encounter the following error:

```
Error initializing application: The calling must be STA, because many UI componenets require this.
```

This is because WPF applications require the Single-Threaded Apartment (STA) threading model. The application has been configured to use STA threading by:

1. Setting the `[STAThread]` attribute on the `Main` method in `Program.cs`
2. Changing the output type to `WinExe` in the project file
3. Adding a `launchSettings.json` file with `"useSTAThread": true`

If you still encounter this error, try running the application using the provided batch file `run_volvo_flash_wr.bat` or by building and running the executable directly:

```bash
dotnet build VolvoFlashWR.Launcher
cd VolvoFlashWR.Launcher\bin\Debug\net8.0-windows
VolvoFlashWR.Launcher.exe
```

### Vocom Driver Not Found

If you encounter an error about the Vocom driver not being found:

1. Make sure you have installed the Vocom1 driver (CommunicationUnitInstaller-*******.msi)
2. Check if the WUDFPuma.dll file exists in the expected locations
3. Configure a custom path as described in the "Custom Driver Path Configuration" section
4. Try running the application in "Dummy Mode" for testing without hardware

## Project Structure

- **VolvoFlashWR.Launcher**: Entry point for the application
- **VolvoFlashWR.UI**: WPF user interface components
- **VolvoFlashWR.Core**: Core business logic and models
- **VolvoFlashWR.Communication**: Communication services for ECU interaction

## Development

To build the application:

```bash
dotnet build
```

To run tests:

```bash
dotnet test
```
