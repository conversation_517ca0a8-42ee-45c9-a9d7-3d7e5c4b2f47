@echo off
echo Starting VolvoFlashWR application in Normal Mode...

REM Clear any existing environment variables first
set "USE_DUMMY_IMPLEMENTATIONS="
set "VERBOSE_LOGGING="
set "LOG_LEVEL="
set "SAFE_MODE="
set "DEMO_MODE="

REM Set environment variables for normal mode with dummy implementations for testing
REM Note: Set to true for safe testing, change to false only when real Vocom hardware is connected
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true

REM Set Phoenix Diag path - skip library checks for now
echo Using dummy implementations for safe testing...

REM Build the solution first with Release configuration for better performance
echo Building solution in Release mode...
dotnet build VolvoFlashWR.sln --configuration Release

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please fix the build errors and run again.
    echo Press any key to exit...
    pause >nul
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!

REM Change to the output directory to ensure proper working directory
cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"

REM Verify critical libraries are present
echo Verifying critical libraries for real hardware mode...

if not exist "WUDFPuma.dll" (
    echo ERROR: WUDFPuma.dll not found - Critical for Vocom communication
    goto :MISSING_LIBS
)

if not exist "Volvo.ApciPlus.dll" (
    echo ERROR: Volvo.ApciPlus.dll not found - Critical for APCI communication
    goto :MISSING_LIBS
)

if not exist "apci.dll" (
    echo ERROR: apci.dll not found - Critical for APCI core functionality
    goto :MISSING_LIBS
)

echo All critical libraries found - Ready for real hardware mode!
goto :CONTINUE

:MISSING_LIBS
echo.
echo CRITICAL ERROR: Missing required libraries for real hardware mode!
echo The application may not work properly with real Vocom adapters.
echo Please check the build configuration and library copying.
echo.
echo Press any key to continue anyway or Ctrl+C to exit...
pause >nul

:CONTINUE

REM Run the application directly (not using start command to avoid environment variable issues)
echo.
echo Starting VolvoFlashWR Launcher...
echo Environment: USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo Environment: VERBOSE_LOGGING=%VERBOSE_LOGGING%

REM Run the application and wait for it to complete
"VolvoFlashWR.Launcher.exe" --mode=Normal

REM Return to original directory
cd /d "%~dp0"

echo Application has exited.
echo Press any key to close this window...
pause >nul
